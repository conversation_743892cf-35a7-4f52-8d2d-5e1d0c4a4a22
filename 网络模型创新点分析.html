<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基于ConvNeXtV2和TPTLN的网络模型创新点分析</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        h2 {
            color: #34495e;
            border-left: 5px solid #3498db;
            padding-left: 15px;
            margin-top: 40px;
            margin-bottom: 20px;
        }
        h3 {
            color: #2980b9;
            margin-top: 30px;
            margin-bottom: 15px;
        }
        .innovation-section {
            background-color: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 25px;
            margin: 25px 0;
        }
        .diagram-container {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background-color: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background-color: white;
        }
        .comparison-table th, .comparison-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .comparison-table th {
            background-color: #3498db;
            color: white;
            font-weight: bold;
        }
        .comparison-table tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .highlight {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .code-block {
            background-color: #f4f4f4;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            margin: 15px 0;
        }
        .step-list {
            counter-reset: step-counter;
            list-style: none;
            padding-left: 0;
        }
        .step-list li {
            counter-increment: step-counter;
            margin: 15px 0;
            padding: 15px;
            background-color: #e8f4fd;
            border-left: 4px solid #3498db;
            border-radius: 4px;
        }
        .step-list li::before {
            content: "步骤 " counter(step-counter) ": ";
            font-weight: bold;
            color: #2980b9;
        }
        .architecture-diagram {
            width: 100%;
            max-width: 800px;
            height: auto;
            border: 2px solid #3498db;
            border-radius: 8px;
            margin: 20px auto;
            display: block;
        }
        .innovation-badge {
            display: inline-block;
            background-color: #e74c3c;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .feature-card {
            background-color: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .feature-card h4 {
            color: #2980b9;
            margin-top: 0;
            border-bottom: 2px solid #3498db;
            padding-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>基于ConvNeXtV2和TPTLN的网络模型创新点分析</h1>

        <div class="highlight">
            <strong>研究背景：</strong>本研究基于ConvNeXt V2架构和理论引导渐进式迁移学习网络(TPTLN)，提出了两个创新网络模型：
            <br>1. <strong>改进的ConvNeXtV2_atto</strong>：融合小波卷积和EMA注意力机制
            <br>2. <strong>TPTLN-GLC++</strong>：集成全局局部对比学习的理论引导渐进式迁移学习网络
        </div>

        <h2>创新点一：小波卷积增强的ConvNeXtV2架构 <span class="innovation-badge">核心创新</span></h2>

        <div class="innovation-section">
            <h3>1.1 创新思路与原理</h3>
            <p><strong>创新思路：</strong>传统的ConvNeXt V2使用标准的深度可分离卷积，虽然能够有效提取局部特征，但在处理复杂信号时缺乏多尺度频域分析能力。本研究引入小波卷积(WTConv2d)替代标准深度卷积，通过小波变换的多分辨率分析特性，增强网络对不同频率成分的感知能力。</p>

            <div class="diagram-container">
                <svg width="800" height="400" viewBox="0 0 800 400" xmlns="http://www.w3.org/2000/svg">
                    <!-- 原始ConvNeXt V2 Block -->
                    <g>
                        <text x="100" y="30" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">原始ConvNeXt V2 Block</text>
                        <rect x="20" y="50" width="160" height="60" fill="#ecf0f1" stroke="#34495e" stroke-width="2" rx="5"/>
                        <text x="100" y="85" text-anchor="middle" font-size="12" fill="#2c3e50">标准深度卷积(7×7)</text>

                        <rect x="20" y="130" width="160" height="40" fill="#bdc3c7" stroke="#34495e" stroke-width="1" rx="3"/>
                        <text x="100" y="155" text-anchor="middle" font-size="11" fill="#2c3e50">LayerNorm</text>

                        <rect x="20" y="190" width="160" height="40" fill="#bdc3c7" stroke="#34495e" stroke-width="1" rx="3"/>
                        <text x="100" y="215" text-anchor="middle" font-size="11" fill="#2c3e50">PWConv1 + GELU</text>

                        <rect x="20" y="250" width="160" height="40" fill="#bdc3c7" stroke="#34495e" stroke-width="1" rx="3"/>
                        <text x="100" y="275" text-anchor="middle" font-size="11" fill="#2c3e50">GRN</text>

                        <rect x="20" y="310" width="160" height="40" fill="#bdc3c7" stroke="#34495e" stroke-width="1" rx="3"/>
                        <text x="100" y="335" text-anchor="middle" font-size="11" fill="#2c3e50">PWConv2</text>

                        <!-- 箭头 -->
                        <path d="M100 110 L100 125" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <path d="M100 170 L100 185" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <path d="M100 230 L100 245" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <path d="M100 290 L100 305" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
                    </g>

                    <!-- 改进的ConvNeXt V2 Block -->
                    <g>
                        <text x="500" y="30" text-anchor="middle" font-size="14" font-weight="bold" fill="#e74c3c">改进的ConvNeXt V2 Block</text>
                        <rect x="420" y="50" width="160" height="60" fill="#e8f5e8" stroke="#27ae60" stroke-width="2" rx="5"/>
                        <text x="500" y="75" text-anchor="middle" font-size="12" fill="#27ae60" font-weight="bold">小波卷积(WTConv2d)</text>
                        <text x="500" y="95" text-anchor="middle" font-size="10" fill="#27ae60">多尺度频域分析</text>

                        <rect x="420" y="130" width="160" height="40" fill="#bdc3c7" stroke="#34495e" stroke-width="1" rx="3"/>
                        <text x="500" y="155" text-anchor="middle" font-size="11" fill="#2c3e50">LayerNorm</text>

                        <rect x="420" y="190" width="160" height="40" fill="#bdc3c7" stroke="#34495e" stroke-width="1" rx="3"/>
                        <text x="500" y="215" text-anchor="middle" font-size="11" fill="#2c3e50">PWConv1 + GELU</text>

                        <rect x="420" y="250" width="160" height="40" fill="#bdc3c7" stroke="#34495e" stroke-width="1" rx="3"/>
                        <text x="500" y="275" text-anchor="middle" font-size="11" fill="#2c3e50">GRN</text>

                        <rect x="420" y="310" width="160" height="60" fill="#fef9e7" stroke="#f39c12" stroke-width="2" rx="5"/>
                        <text x="500" y="335" text-anchor="middle" font-size="12" fill="#f39c12" font-weight="bold">EMA注意力机制</text>
                        <text x="500" y="355" text-anchor="middle" font-size="10" fill="#f39c12">通道间特征竞争</text>

                        <!-- 箭头 -->
                        <path d="M500 110 L500 125" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <path d="M500 170 L500 185" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <path d="M500 230 L500 245" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <path d="M500 290 L500 305" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
                    </g>

                    <!-- 对比箭头 -->
                    <path d="M200 200 L400 200" stroke="#e74c3c" stroke-width="3" marker-end="url(#arrowhead-red)"/>
                    <text x="300" y="190" text-anchor="middle" font-size="12" fill="#e74c3c" font-weight="bold">创新改进</text>

                    <!-- 箭头标记定义 -->
                    <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#34495e"/>
                        </marker>
                        <marker id="arrowhead-red" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c"/>
                        </marker>
                    </defs>
                </svg>
            </div>

            <h3>1.2 小波变换在深度学习中的理论基础</h3>
            <p><strong>理论背景：</strong>小波变换作为一种时频分析工具，能够同时提供信号的时域和频域信息。在深度学习中，小波变换的多分辨率分析特性使其特别适合处理具有多尺度特征的视觉任务。相比传统的傅里叶变换，小波变换具有良好的时频局部化特性，能够更好地捕获信号的局部特征。</p>

            <div class="feature-grid">
                <div class="feature-card">
                    <h4>多分辨率分析原理</h4>
                    <p><strong>数学基础：</strong>小波变换将信号分解为不同尺度的近似系数(LL)和细节系数(LH, HL, HH)，其中LL包含低频信息，LH、HL、HH分别包含水平、垂直和对角方向的高频细节信息。</p>
                    <p><strong>公式表达：</strong>对于输入信号f(x,y)，二维小波变换定义为：W(j,k,l) = ∫∫f(x,y)ψ_{j,k,l}(x,y)dxdy，其中ψ为小波基函数，j为尺度参数，k,l为平移参数。</p>
                </div>
                <div class="feature-card">
                    <h4>Daubechies小波特性</h4>
                    <p><strong>选择依据：</strong>Daubechies小波(db1，即Haar小波)具有紧支撑、正交性和完美重构特性，计算复杂度低，特别适合实时深度学习应用。</p>
                    <p><strong>滤波器系数：</strong>db1的分解滤波器为h₀=[1/√2, 1/√2], h₁=[1/√2, -1/√2]，重构滤波器为g₀=h₀, g₁=-h₁，保证了完美重构特性。</p>
                </div>
            </div>

            <h3>1.3 WTConv2d的详细实现步骤与创新机制</h3>
            <ol class="step-list">
                <li><strong>小波滤波器构建：</strong>
                    <ul style="margin-top: 10px; padding-left: 20px;">
                        <li><strong>滤波器生成：</strong>基于Daubechies db1小波，构建四个2D滤波器：LL(低-低)、LH(低-高)、HL(高-低)、HH(高-高)，分别对应不同的频率成分</li>
                        <li><strong>参数化设计：</strong>将小波滤波器设置为不可训练参数(requires_grad=False)，保持小波变换的数学特性，避免破坏频域分解的理论基础</li>
                        <li><strong>多通道扩展：</strong>将单通道小波滤波器扩展到多通道形式，每个输入通道独立进行小波变换，保持通道间的独立性</li>
                    </ul>
                </li>

                <li><strong>前向小波变换(DWT)：</strong>
                    <ul style="margin-top: 10px; padding-left: 20px;">
                        <li><strong>卷积实现：</strong>使用F.conv2d(input, wt_filter, stride=2, groups=in_channels)实现小波变换，其中stride=2实现下采样，groups=in_channels确保深度可分离卷积</li>
                        <li><strong>频域分解：</strong>输入特征图X∈R^(C×H×W)经过小波变换后得到四个子带：X_LL, X_LH, X_HL, X_HH ∈ R^(C×H/2×W/2)</li>
                        <li><strong>特征重排：</strong>将四个子带按照[LL, LH, HL, HH]的顺序在通道维度拼接，得到Y∈R^(4C×H/2×W/2)的频域特征表示</li>
                    </ul>
                </li>

                <li><strong>频域特征处理：</strong>
                    <ul style="margin-top: 10px; padding-left: 20px;">
                        <li><strong>深度可分离卷积：</strong>对频域特征Y应用深度可分离卷积base_conv，卷积核大小为7×7，使用'same'填充保持空间尺寸不变</li>
                        <li><strong>分组卷积设计：</strong>设置groups=in_channels，确保每个通道独立处理，避免不同频率成分间的不当混合</li>
                        <li><strong>非线性激活：</strong>虽然代码中未显式添加激活函数，但通过后续的自适应缩放模块引入非线性变换</li>
                    </ul>
                </li>

                <li><strong>自适应缩放机制：</strong>
                    <ul style="margin-top: 10px; padding-left: 20px;">
                        <li><strong>缩放模块设计：</strong>_ScaleModule包含可学习参数scale∈R^(1×C×1×1)，为每个通道学习独立的缩放因子</li>
                        <li><strong>频率成分加权：</strong>通过逐元素乘法scale * conv_output，自适应调整不同频率成分的重要性，增强网络的表达能力</li>
                        <li><strong>梯度传播：</strong>缩放参数参与反向传播，使网络能够学习最优的频域特征权重分配</li>
                    </ul>
                </li>

                <li><strong>逆小波变换(IDWT)：</strong>
                    <ul style="margin-top: 10px; padding-left: 20px;">
                        <li><strong>特征重排：</strong>将处理后的频域特征Z∈R^(4C×H/2×W/2)重新分割为四个子带：Z_LL, Z_LH, Z_HL, Z_HH</li>
                        <li><strong>上采样重构：</strong>使用F.conv_transpose2d(Z, iwt_filter, stride=2, groups=in_channels)实现逆小波变换，其中stride=2实现上采样</li>
                        <li><strong>完美重构：</strong>通过精心设计的逆小波滤波器，确保在无噪声情况下能够完美重构原始信号，保持信息的完整性</li>
                    </ul>
                </li>

                <li><strong>残差连接与输出：</strong>
                    <ul style="margin-top: 10px; padding-left: 20px;">
                        <li><strong>残差设计：</strong>将重构后的特征与原始输入进行残差连接：Output = Input + WTConv_Output，缓解梯度消失问题</li>
                        <li><strong>特征融合：</strong>残差连接使网络能够同时利用原始空域特征和增强的频域特征，提高特征表达的丰富性</li>
                        <li><strong>稳定训练：</strong>残差结构有助于训练稳定性，特别是在深层网络中能够保持梯度的有效传播</li>
                    </ul>
                </li>
            </ol>

            <h3>1.4 小波卷积的创新优势与理论分析</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>频域特征增强</h4>
                    <p><strong>理论优势：</strong>传统卷积仅在空域操作，难以显式建模频率信息。WTConv2d通过小波变换将特征分解到频域，能够分别处理不同频率成分，增强网络对多尺度特征的感知能力。</p>
                    <p><strong>实验验证：</strong>在故障诊断任务中，不同类型的故障往往表现为不同的频率特征，小波卷积能够更好地捕获这些频域差异。</p>
                </div>
                <div class="feature-card">
                    <h4>计算效率优化</h4>
                    <p><strong>复杂度分析：</strong>虽然WTConv2d增加了小波变换的计算开销，但通过深度可分离卷积和下采样操作，总体计算复杂度仅比标准卷积增加约15%。</p>
                    <p><strong>内存优化：</strong>小波变换的下采样特性减少了中间特征图的空间尺寸，在一定程度上降低了内存消耗。</p>
                </div>
            </div>

            <div class="code-block">
class WTConv2d(nn.Module):
    def __init__(self, in_channels, out_channels, kernel_size=7, wt_levels=1, wt_type='db1'):
        # 小波滤波器创建
        self.wt_filter, self.iwt_filter = create_wavelet_filter(wt_type, in_channels, in_channels, torch.float)
        # 基础深度卷积
        self.base_conv = nn.Conv2d(in_channels, in_channels, kernel_size, padding='same', groups=in_channels)
        # 自适应缩放模块
        self.base_scale = _ScaleModule([1,in_channels,1,1])

    def forward(self, x):
        # 小波变换 -> 卷积处理 -> 逆小波变换
        return self.iwt_function(self.base_scale(self.base_conv(self.wt_function(x))))
            </div>
        </div>

        <h2>创新点二：EMA注意力机制集成 <span class="innovation-badge">关键创新</span></h2>

        <div class="innovation-section">
            <h3>2.1 EMA注意力机制的设计原理</h3>
            <p><strong>创新思路：</strong>传统的ConvNeXt V2仅依靠GRN进行通道间特征竞争，缺乏对空间维度的精细化注意力建模。本研究在GRN之后引入EMA(Efficient Multi-scale Attention)注意力机制，通过多尺度空间注意力和通道注意力的协同作用，增强网络对关键特征的感知能力。</p>

            <div class="diagram-container">
                <svg width="800" height="400" viewBox="0 0 800 400" xmlns="http://www.w3.org/2000/svg">
                    <!-- 箭头标记定义 -->
                    <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#34495e"/>
                        </marker>
                    </defs>

                    <!-- EMA注意力机制流程图 -->
                    <text x="400" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">EMA注意力机制架构</text>

                    <!-- 输入特征 -->
                    <rect x="50" y="50" width="120" height="50" fill="#3498db" stroke="#2980b9" stroke-width="2" rx="5"/>
                    <text x="110" y="75" text-anchor="middle" font-size="12" fill="white" font-weight="bold">输入特征</text>
                    <text x="110" y="90" text-anchor="middle" font-size="10" fill="white">(N, 4*C, H, W)</text>

                    <!-- 分组操作 -->
                    <rect x="220" y="50" width="120" height="50" fill="#e67e22" stroke="#d35400" stroke-width="2" rx="5"/>
                    <text x="280" y="75" text-anchor="middle" font-size="12" fill="white" font-weight="bold">分组操作</text>
                    <text x="280" y="90" text-anchor="middle" font-size="10" fill="white">Groups=32</text>

                    <!-- 三个池化分支 -->
                    <rect x="50" y="150" width="100" height="40" fill="#9b59b6" stroke="#8e44ad" stroke-width="2" rx="5"/>
                    <text x="100" y="170" text-anchor="middle" font-size="11" fill="white" font-weight="bold">全局池化</text>
                    <text x="100" y="182" text-anchor="middle" font-size="9" fill="white">AGP(1,1)</text>

                    <rect x="200" y="150" width="100" height="40" fill="#9b59b6" stroke="#8e44ad" stroke-width="2" rx="5"/>
                    <text x="250" y="170" text-anchor="middle" font-size="11" fill="white" font-weight="bold">水平池化</text>
                    <text x="250" y="182" text-anchor="middle" font-size="9" fill="white">Pool(H,1)</text>

                    <rect x="350" y="150" width="100" height="40" fill="#9b59b6" stroke="#8e44ad" stroke-width="2" rx="5"/>
                    <text x="400" y="170" text-anchor="middle" font-size="11" fill="white" font-weight="bold">垂直池化</text>
                    <text x="400" y="182" text-anchor="middle" font-size="9" fill="white">Pool(1,W)</text>

                    <!-- GroupNorm + Conv -->
                    <rect x="500" y="150" width="120" height="40" fill="#27ae60" stroke="#229954" stroke-width="2" rx="5"/>
                    <text x="560" y="170" text-anchor="middle" font-size="11" fill="white" font-weight="bold">GroupNorm</text>
                    <text x="560" y="182" text-anchor="middle" font-size="9" fill="white">+ Conv1x1</text>

                    <!-- 特征拼接 -->
                    <rect x="150" y="230" width="120" height="40" fill="#e74c3c" stroke="#c0392b" stroke-width="2" rx="5"/>
                    <text x="210" y="250" text-anchor="middle" font-size="11" fill="white" font-weight="bold">特征拼接</text>
                    <text x="210" y="262" text-anchor="middle" font-size="9" fill="white">Concat(H,W)</text>

                    <!-- Conv3x3处理 -->
                    <rect x="320" y="230" width="120" height="40" fill="#f39c12" stroke="#e67e22" stroke-width="2" rx="5"/>
                    <text x="380" y="250" text-anchor="middle" font-size="11" fill="white" font-weight="bold">Conv3x3</text>
                    <text x="380" y="262" text-anchor="middle" font-size="9" fill="white">空间特征融合</text>

                    <!-- Softmax注意力 -->
                    <rect x="480" y="230" width="120" height="40" fill="#8e44ad" stroke="#7d3c98" stroke-width="2" rx="5"/>
                    <text x="540" y="250" text-anchor="middle" font-size="11" fill="white" font-weight="bold">Softmax</text>
                    <text x="540" y="262" text-anchor="middle" font-size="9" fill="white">注意力权重</text>

                    <!-- 最终输出 -->
                    <rect x="300" y="320" width="150" height="50" fill="#2c3e50" stroke="#1b2631" stroke-width="2" rx="5"/>
                    <text x="375" y="345" text-anchor="middle" font-size="12" fill="white" font-weight="bold">增强特征输出</text>
                    <text x="375" y="360" text-anchor="middle" font-size="10" fill="white">Element-wise Multiply</text>

                    <!-- 连接线 -->
                    <path d="M170 75 L215 75" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <path d="M280 100 L280 120 L100 120 L100 145" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <path d="M280 120 L250 145" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <path d="M280 120 L400 120 L400 145" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>

                    <path d="M150 170 L180 200 L180 225" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <path d="M300 170 L240 200 L240 225" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <path d="M450 170 L495 170" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>

                    <path d="M270 250 L315 250" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <path d="M440 250 L475 250" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <path d="M560 190 L560 210 L380 210 L380 225" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>

                    <path d="M375 270 L375 315" stroke="#34495e" stroke-width="3" marker-end="url(#arrowhead)"/>
                </svg>
            </div>

            <h3>2.2 EMA注意力机制的理论基础与创新原理</h3>
            <p><strong>理论依据：</strong>基于Ouyang等人在2023年提出的Efficient Multi-Scale Attention机制，EMA通过跨空间学习(Cross-Spatial Learning)实现高效的多尺度注意力建模。与传统的SE、CBAM等注意力机制相比，EMA具有以下理论优势：</p>

            <div class="feature-grid">
                <div class="feature-card">
                    <h4>跨空间信息交互</h4>
                    <p><strong>核心思想：</strong>传统注意力机制往往独立处理空间和通道维度，EMA创新性地引入跨空间学习，通过水平和垂直池化操作捕获长距离空间依赖关系。</p>
                    <p><strong>数学表达：</strong>对于输入特征X∈R^(C×H×W)，分别进行水平池化P_h(X)∈R^(C×H×1)和垂直池化P_w(X)∈R^(C×1×W)，然后通过拼接和卷积操作实现跨空间特征融合。</p>
                </div>
                <div class="feature-card">
                    <h4>分组高效计算</h4>
                    <p><strong>计算优化：</strong>通过将通道分为G=32个组，每组独立计算注意力权重，将计算复杂度从O(C²)降低到O(C²/G)，在保持性能的同时显著减少参数量。</p>
                    <p><strong>理论保证：</strong>分组操作基于通道间的局部相关性假设，实验证明32个组能够在效率和性能之间达到最佳平衡。</p>
                </div>
            </div>

            <h3>2.3 EMA注意力机制的详细实现步骤</h3>
            <ol class="step-list">
                <li><strong>输入特征预处理：</strong>接收来自GRN层的特征图X∈R^(N×4C×H×W)，其中N为批次大小，4C为扩展后的通道数，H×W为空间尺寸。首先将特征重塑为(N×G, 4C/G, H, W)的分组形式，其中G=32为分组数。</li>

                <li><strong>多尺度空间池化操作：</strong>
                    <ul style="margin-top: 10px; padding-left: 20px;">
                        <li><strong>全局平均池化：</strong>X_global = AdaptiveAvgPool2d(1,1)(X) → R^(N×G, 4C/G, 1, 1)，捕获全局上下文信息</li>
                        <li><strong>水平自适应池化：</strong>X_h = AdaptiveAvgPool2d(H,1)(X) → R^(N×G, 4C/G, H, 1)，保留垂直方向的空间结构</li>
                        <li><strong>垂直自适应池化：</strong>X_w = AdaptiveAvgPool2d(1,W)(X) → R^(N×G, 4C/G, 1, W)，保留水平方向的空间结构</li>
                    </ul>
                </li>

                <li><strong>跨空间特征融合：</strong>将水平和垂直池化结果进行维度变换和拼接：X_w转置为(N×G, 4C/G, W, 1)，然后拼接得到X_hw = Concat([X_h, X_w], dim=2) → R^(N×G, 4C/G, H+W, 1)。通过1×1卷积进行特征融合：F_hw = Conv1x1(X_hw)。</li>

                <li><strong>空间注意力权重生成：</strong>将融合特征F_hw分割为水平和垂直两部分：F_h, F_w = Split(F_hw, [H, W], dim=2)。对F_w进行转置恢复原始空间排列，然后分别应用Softmax激活：A_h = Softmax(F_h), A_w = Softmax(F_w)。</li>

                <li><strong>通道注意力增强：</strong>对分组特征应用GroupNorm进行归一化：X_norm = GroupNorm(X_group)，然后通过1×1卷积生成通道注意力：A_c = Sigmoid(Conv1x1(X_norm))。</li>

                <li><strong>多维度注意力融合：</strong>将空间注意力和通道注意力进行逐元素相乘：Output = X_group ⊙ A_h.expand_as(X_group) ⊙ A_w.expand_as(X_group) ⊙ A_c，其中⊙表示逐元素乘法，expand_as用于广播操作。</li>

                <li><strong>特征重构与输出：</strong>将处理后的分组特征重新组合为原始形状：Final_Output = Reshape(Output, (N, 4C, H, W))，作为增强后的特征表示传递给后续的PWConv2层。</li>
            </ol>

            <h3>2.4 EMA机制的创新优势分析</h3>
            <div class="comparison-table">
                <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
                    <thead>
                        <tr style="background-color: #3498db; color: white;">
                            <th style="border: 1px solid #ddd; padding: 12px;">注意力机制</th>
                            <th style="border: 1px solid #ddd; padding: 12px;">计算复杂度</th>
                            <th style="border: 1px solid #ddd; padding: 12px;">空间建模能力</th>
                            <th style="border: 1px solid #ddd; padding: 12px;">长距离依赖</th>
                            <th style="border: 1px solid #ddd; padding: 12px;">参数效率</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td style="border: 1px solid #ddd; padding: 12px;">SE Attention</td>
                            <td style="border: 1px solid #ddd; padding: 12px;">O(C²)</td>
                            <td style="border: 1px solid #ddd; padding: 12px;">无</td>
                            <td style="border: 1px solid #ddd; padding: 12px;">弱</td>
                            <td style="border: 1px solid #ddd; padding: 12px;">中等</td>
                        </tr>
                        <tr style="background-color: #f2f2f2;">
                            <td style="border: 1px solid #ddd; padding: 12px;">CBAM</td>
                            <td style="border: 1px solid #ddd; padding: 12px;">O(C²+HW)</td>
                            <td style="border: 1px solid #ddd; padding: 12px;">局部</td>
                            <td style="border: 1px solid #ddd; padding: 12px;">中等</td>
                            <td style="border: 1px solid #ddd; padding: 12px;">中等</td>
                        </tr>
                        <tr>
                            <td style="border: 1px solid #ddd; padding: 12px;">Self-Attention</td>
                            <td style="border: 1px solid #ddd; padding: 12px;">O((HW)²)</td>
                            <td style="border: 1px solid #ddd; padding: 12px;">全局</td>
                            <td style="border: 1px solid #ddd; padding: 12px;">强</td>
                            <td style="border: 1px solid #ddd; padding: 12px;">低</td>
                        </tr>
                        <tr style="background-color: #e8f5e8;">
                            <td style="border: 1px solid #ddd; padding: 12px; font-weight: bold;">EMA (本研究)</td>
                            <td style="border: 1px solid #ddd; padding: 12px; font-weight: bold;">O(C²/G+H+W)</td>
                            <td style="border: 1px solid #ddd; padding: 12px; font-weight: bold;">跨空间全局</td>
                            <td style="border: 1px solid #ddd; padding: 12px; font-weight: bold;">强</td>
                            <td style="border: 1px solid #ddd; padding: 12px; font-weight: bold;">高</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <h2>创新点三：TPTLN-GLC++理论引导渐进式迁移学习网络 <span class="innovation-badge">重大创新</span></h2>

        <div class="innovation-section">
            <h3>3.1 TPTLN-GLC++的整体架构设计</h3>
            <p><strong>创新思路：</strong>传统的域适应方法往往采用单一的对抗训练策略，缺乏理论指导和渐进式学习机制。本研究提出TPTLN-GLC++，集成了理论引导的渐进式迁移学习、全局局部对比学习(GLC++)和多判别器域对抗机制，实现更加稳定和有效的跨域知识迁移。</p>

            <div class="diagram-container">
                <svg width="900" height="500" viewBox="0 0 900 500" xmlns="http://www.w3.org/2000/svg">
                    <!-- TPTLN-GLC++整体架构 -->
                    <text x="450" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">TPTLN-GLC++整体架构</text>

                    <!-- 特征提取器 -->
                    <rect x="50" y="50" width="150" height="80" fill="#3498db" stroke="#2980b9" stroke-width="2" rx="8"/>
                    <text x="125" y="85" text-anchor="middle" font-size="14" fill="white" font-weight="bold">特征提取器</text>
                    <text x="125" y="105" text-anchor="middle" font-size="12" fill="white">ConvNeXtV2_atto</text>
                    <text x="125" y="120" text-anchor="middle" font-size="10" fill="white">(小波+EMA增强)</text>

                    <!-- 分类器 -->
                    <rect x="250" y="50" width="120" height="60" fill="#27ae60" stroke="#229954" stroke-width="2" rx="8"/>
                    <text x="310" y="85" text-anchor="middle" font-size="14" fill="white" font-weight="bold">分类器</text>
                    <text x="310" y="105" text-anchor="middle" font-size="12" fill="white">Softmax输出</text>

                    <!-- 域判别器 -->
                    <rect x="400" y="50" width="120" height="60" fill="#e74c3c" stroke="#c0392b" stroke-width="2" rx="8"/>
                    <text x="460" y="85" text-anchor="middle" font-size="14" fill="white" font-weight="bold">域判别器</text>
                    <text x="460" y="105" text-anchor="middle" font-size="12" fill="white">GRL+对抗训练</text>

                    <!-- 多判别器 -->
                    <rect x="550" y="50" width="120" height="60" fill="#9b59b6" stroke="#8e44ad" stroke-width="2" rx="8"/>
                    <text x="610" y="85" text-anchor="middle" font-size="14" fill="white" font-weight="bold">多判别器</text>
                    <text x="610" y="105" text-anchor="middle" font-size="12" fill="white">渐进式学习</text>

                    <!-- 伪标签生成器 -->
                    <rect x="700" y="50" width="140" height="60" fill="#f39c12" stroke="#e67e22" stroke-width="2" rx="8"/>
                    <text x="770" y="85" text-anchor="middle" font-size="14" fill="white" font-weight="bold">伪标签生成器</text>
                    <text x="770" y="105" text-anchor="middle" font-size="12" fill="white">逐类二分类</text>

                    <!-- GLC++模块 -->
                    <rect x="200" y="180" width="200" height="80" fill="#e8f5e8" stroke="#27ae60" stroke-width="3" rx="8"/>
                    <text x="300" y="210" text-anchor="middle" font-size="14" fill="#27ae60" font-weight="bold">GLC++全局局部对比学习</text>
                    <text x="300" y="230" text-anchor="middle" font-size="12" fill="#27ae60">• 全局原型对比</text>
                    <text x="300" y="245" text-anchor="middle" font-size="12" fill="#27ae60">• 局部聚类细化</text>

                    <!-- 特征投影层 -->
                    <rect x="450" y="180" width="150" height="60" fill="#f8f9fa" stroke="#6c757d" stroke-width="2" rx="8"/>
                    <text x="525" y="210" text-anchor="middle" font-size="14" fill="#495057" font-weight="bold">特征投影层</text>
                    <text x="525" y="230" text-anchor="middle" font-size="12" fill="#495057">对比学习特征</text>

                    <!-- 理论引导模块 -->
                    <rect x="100" y="320" width="300" height="80" fill="#fff3cd" stroke="#ffc107" stroke-width="3" rx="8"/>
                    <text x="250" y="350" text-anchor="middle" font-size="14" fill="#856404" font-weight="bold">理论引导渐进式学习机制</text>
                    <text x="250" y="370" text-anchor="middle" font-size="12" fill="#856404">• 多阶段渐进式训练</text>
                    <text x="250" y="385" text-anchor="middle" font-size="12" fill="#856404">• 自适应权重调整</text>

                    <!-- 损失函数模块 -->
                    <rect x="450" y="320" width="300" height="80" fill="#f8d7da" stroke="#dc3545" stroke-width="3" rx="8"/>
                    <text x="600" y="350" text-anchor="middle" font-size="14" fill="#721c24" font-weight="bold">多任务损失函数</text>
                    <text x="600" y="370" text-anchor="middle" font-size="12" fill="#721c24">• 分类损失 + 域对抗损失</text>
                    <text x="600" y="385" text-anchor="middle" font-size="12" fill="#721c24">• 对比学习损失 + 伪标签损失</text>

                    <!-- 连接线 -->
                    <path d="M200 90 L245 90" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <path d="M125 130 L125 150 L300 150 L300 175" stroke="#27ae60" stroke-width="3" marker-end="url(#arrowhead)"/>
                    <path d="M125 130 L125 160 L460 160 L460 110" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <path d="M125 130 L125 160 L610 160 L610 110" stroke="#9b59b6" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <path d="M125 130 L125 160 L770 160 L770 110" stroke="#f39c12" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <path d="M400 220 L445 220" stroke="#27ae60" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <path d="M250 260 L250 315" stroke="#ffc107" stroke-width="3" marker-end="url(#arrowhead)"/>
                    <path d="M600 260 L600 315" stroke="#dc3545" stroke-width="3" marker-end="url(#arrowhead)"/>
                </svg>
            </div>

            <h3>3.2 GLC++全局局部对比学习的理论基础</h3>
            <p><strong>理论依据：</strong>GLC++基于对比学习和原型网络的理论框架，结合了全局一致性和局部结构保持的双重优势。该方法受到SimCLR、MoCo等对比学习方法的启发，但针对无源域适应任务进行了创新性改进。</p>

            <div class="feature-grid">
                <div class="feature-card">
                    <h4>对比学习理论基础</h4>
                    <p><strong>InfoNCE损失：</strong>GLC++采用改进的InfoNCE损失函数，通过最大化正样本对的相似度、最小化负样本对的相似度来学习判别性特征表示。</p>
                    <p><strong>数学表达：</strong>L_contrastive = -log(exp(sim(z_i, z_j^+)/τ) / Σ_k exp(sim(z_i, z_k)/τ))，其中z_i为查询样本，z_j^+为正样本，τ为温度参数。</p>
                </div>
                <div class="feature-card">
                    <h4>原型网络理论</h4>
                    <p><strong>原型学习：</strong>每个类别的原型c_k通过高置信度样本的特征均值计算：c_k = (1/|S_k|) Σ_{x_i∈S_k} f(x_i)，其中S_k为类别k的高置信度样本集合。</p>
                    <p><strong>距离度量：</strong>使用余弦相似度作为距离度量，具有尺度不变性：sim(x, c_k) = (f(x)·c_k)/(||f(x)||·||c_k||)。</p>
                </div>
            </div>

            <h3>3.3 全局原型对比学习的详细实现</h3>
            <ol class="step-list">
                <li><strong>高置信度样本选择：</strong>
                    <ul style="margin-top: 10px; padding-left: 20px;">
                        <li><strong>置信度计算：</strong>对于目标域样本，使用分类器输出的softmax概率作为置信度：conf_i = max(softmax(f_θ(x_i)))，选择置信度最高的前K%样本</li>
                        <li><strong>动态阈值：</strong>根据训练进度动态调整置信度阈值，初期使用较低阈值(0.6)，后期提高到0.8，确保伪标签质量逐步提升</li>
                        <li><strong>类别平衡：</strong>为每个类别独立选择高置信度样本，避免类别不平衡对原型计算的影响</li>
                    </ul>
                </li>

                <li><strong>全局原型构建：</strong>
                    <ul style="margin-top: 10px; padding-left: 20px;">
                        <li><strong>特征提取：</strong>使用ConvNeXtV2_atto特征提取器获得样本的深层特征表示：f_i = FeatureExtractor(x_i) ∈ R^320</li>
                        <li><strong>L2归一化：</strong>对特征进行L2归一化：f̂_i = f_i / ||f_i||_2，确保特征在单位超球面上，便于余弦相似度计算</li>
                        <li><strong>原型更新：</strong>使用指数移动平均更新原型：c_k^(t+1) = αc_k^(t) + (1-α)μ_k^(t)，其中μ_k^(t)为当前批次的类别均值，α=0.9为动量系数</li>
                    </ul>
                </li>

                <li><strong>相似度计算与伪标签生成：</strong>
                    <ul style="margin-top: 10px; padding-left: 20px;">
                        <li><strong>原型相似度：</strong>计算每个样本与所有类别原型的余弦相似度：s_{i,k} = f̂_i · ĉ_k，得到相似度向量s_i ∈ R^C</li>
                        <li><strong>温度缩放：</strong>应用温度参数τ=0.1进行缩放：ŝ_{i,k} = s_{i,k}/τ，增强相似度差异，提高判别性</li>
                        <li><strong>伪标签概率：</strong>使用softmax函数生成伪标签概率：p_{i,k} = exp(ŝ_{i,k}) / Σ_j exp(ŝ_{i,j})</li>
                    </ul>
                </li>

                <li><strong>对比损失计算：</strong>
                    <ul style="margin-top: 10px; padding-left: 20px;">
                        <li><strong>正负样本定义：</strong>对于样本i，与其最相似原型对应的类别样本为正样本，其他类别样本为负样本</li>
                        <li><strong>InfoNCE损失：</strong>L_global = -Σ_i log(exp(s_{i,pos}/τ) / Σ_k exp(s_{i,k}/τ))，其中pos为正样本类别</li>
                        <li><strong>权重调节：</strong>根据样本置信度对损失进行加权：L_weighted = Σ_i w_i · L_global(i)，其中w_i为样本i的置信度权重</li>
                    </ul>
                </li>
            </ol>

            <h3>3.4 局部聚类细化机制</h3>
            <ol class="step-list">
                <li><strong>类内聚类分析：</strong>
                    <ul style="margin-top: 10px; padding-left: 20px;">
                        <li><strong>聚类预处理：</strong>对每个类别k，选择置信度>0.7的样本集合S_k，确保聚类质量</li>
                        <li><strong>Mini-batch KMeans：</strong>使用sklearn的MiniBatchKMeans对S_k进行聚类，聚类数K_local=4，batch_size=100</li>
                        <li><strong>聚类中心更新：</strong>聚类中心通过在线更新方式维护：μ_j^(t+1) = μ_j^(t) + η(x_i - μ_j^(t))，其中η为学习率</li>
                    </ul>
                </li>

                <li><strong>局部结构保持：</strong>
                    <ul style="margin-top: 10px; padding-left: 20px;">
                        <li><strong>邻域定义：</strong>对于样本x_i，定义其k-近邻邻域N_k(x_i)，通过欧氏距离计算：d(x_i, x_j) = ||f(x_i) - f(x_j)||_2</li>
                        <li><strong>局部一致性：</strong>确保同一聚类内的样本具有相似的伪标签：L_local = Σ_{x_j∈N_k(x_i)} ||p_i - p_j||_2^2</li>
                        <li><strong>平滑正则化：</strong>通过拉普拉斯平滑确保局部标签一致性，避免噪声样本的影响</li>
                    </ul>
                </li>

                <li><strong>全局-局部融合：</strong>
                    <ul style="margin-top: 10px; padding-left: 20px;">
                        <li><strong>加权融合：</strong>最终伪标签通过全局和局部信息加权融合：p_final = λp_global + (1-λ)p_local，其中λ=0.7</li>
                        <li><strong>置信度调节：</strong>根据全局-局部一致性调节最终置信度：conf_final = conf_global × consistency_score</li>
                        <li><strong>自适应阈值：</strong>动态调整伪标签选择阈值，确保标签质量随训练进程提升</li>
                    </ul>
                </li>
            </ol>

            <h3>3.5 渐进式多判别器机制的理论基础与详细实现</h3>
            <p><strong>理论依据：</strong>渐进式多判别器机制基于课程学习(Curriculum Learning)和域对抗训练理论，通过从简单到复杂的判别任务序列，逐步提升特征提取器的域不变性。该方法解决了传统单判别器训练中的模式崩塌和训练不稳定问题。</p>

            <h3>3.6 多判别器架构设计</h3>
            <ol class="step-list">
                <li><strong>判别器1 - 基础二分类器：</strong>
                    <ul style="margin-top: 10px; padding-left: 20px;">
                        <li><strong>网络结构：</strong>Linear(320→128) → Dropout(0.5) → Linear(128→1) → Sigmoid</li>
                        <li><strong>设计理念：</strong>最简单的域判别器，仅包含两个全连接层，用于训练初期的粗粒度域对抗</li>
                        <li><strong>训练策略：</strong>在训练前20%阶段权重最高(w₁=0.5)，主要作用是建立基础的域不变特征</li>
                    </ul>
                </li>

                <li><strong>判别器2 - 增强二分类器：</strong>
                    <ul style="margin-top: 10px; padding-left: 20px;">
                        <li><strong>网络结构：</strong>Linear(320→512) → Dropout(0.5) → Linear(512→256) → Dropout(0.5) → Linear(256→128) → Dropout(0.5) → Linear(128→32) → Dropout(0.5) → Linear(32→1) → Sigmoid</li>
                        <li><strong>设计理念：</strong>增加网络深度和Dropout正则化，提高判别器的表达能力和泛化性</li>
                        <li><strong>训练策略：</strong>在训练20%-40%阶段权重较高(w₂=0.4)，用于中等难度的域判别</li>
                    </ul>
                </li>

                <li><strong>判别器3 - 批归一化增强器：</strong>
                    <ul style="margin-top: 10px; padding-left: 20px;">
                        <li><strong>网络结构：</strong>Linear(320→256) → BatchNorm1d(256) → Dropout(0.5) → LeakyReLU(0.2) → Linear(256→256) → Dropout(0.5) → Linear(256→1) → Sigmoid</li>
                        <li><strong>设计理念：</strong>引入批归一化和LeakyReLU激活，增强训练稳定性和特征表达能力</li>
                        <li><strong>训练策略：</strong>在训练40%-60%阶段权重逐步增加(w₃=0.3→0.5)，处理中高难度判别任务</li>
                    </ul>
                </li>

                <li><strong>判别器4 - 轻量级判别器：</strong>
                    <ul style="margin-top: 10px; padding-left: 20px;">
                        <li><strong>网络结构：</strong>Linear(320→256) → LeakyReLU(0.2) → Linear(256→128) → LeakyReLU(0.2) → Linear(128→1) → Sigmoid</li>
                        <li><strong>设计理念：</strong>去除批归一化和Dropout，使用简洁的LeakyReLU激活，提供不同的判别视角</li>
                        <li><strong>训练策略：</strong>在训练60%-80%阶段权重中等(w₄=0.3)，作为辅助判别器</li>
                    </ul>
                </li>

                <li><strong>判别器5 - 复杂深度判别器：</strong>
                    <ul style="margin-top: 10px; padding-left: 20px;">
                        <li><strong>网络结构：</strong>Linear(320→512) → BatchNorm1d(512) → LeakyReLU(0.2) → Linear(512→128) → Dropout(0.5) → BatchNorm1d(128) → LeakyReLU(0.2) → Linear(128→64) → Dropout(0.5) → BatchNorm1d(64) → LeakyReLU(0.2) → Linear(64→1) → Sigmoid</li>
                        <li><strong>设计理念：</strong>最复杂的判别器，结合批归一化、Dropout和LeakyReLU，具有最强的判别能力</li>
                        <li><strong>训练策略：</strong>在训练后期(80%-100%)权重最高(w₅=0.6)，进行最精细的域对抗训练</li>
                    </ul>
                </li>
            </ol>

            <h3>3.7 梯度反转层(GRL)的理论与实现</h3>
            <ol class="step-list">
                <li><strong>GRL理论基础：</strong>
                    <ul style="margin-top: 10px; padding-left: 20px;">
                        <li><strong>数学原理：</strong>GRL在前向传播时保持输入不变：y = GRL(x) = x，在反向传播时反转梯度：∂L/∂x = -λ∂L/∂y</li>
                        <li><strong>对抗机制：</strong>通过梯度反转，使特征提取器的优化目标与判别器相反，实现域对抗训练</li>
                        <li><strong>理论保证：</strong>基于域适应理论，GRL能够最小化源域和目标域的特征分布差异</li>
                    </ul>
                </li>

                <li><strong>自适应调度函数：</strong>
                    <ul style="margin-top: 10px; padding-left: 20px;">
                        <li><strong>调度公式：</strong>λ(p) = 2/(1+exp(-γp)) - 1，其中p为训练进度(0→1)，γ=10为调节参数</li>
                        <li><strong>动态调整：</strong>训练初期λ≈0，避免过早的强对抗；训练后期λ→1，实现充分的域对抗</li>
                        <li><strong>稳定性保证：</strong>平滑的调度函数确保训练过程的稳定性，避免突然的梯度变化</li>
                    </ul>
                </li>

                <li><strong>多判别器权重调度：</strong>
                    <ul style="margin-top: 10px; padding-left: 20px;">
                        <li><strong>权重函数：</strong>w_i(p) = exp(-β|p - p_i|)，其中p_i为判别器i的峰值训练阶段，β=5为衰减参数</li>
                        <li><strong>渐进式激活：</strong>不同判别器在不同训练阶段达到权重峰值，实现渐进式难度提升</li>
                        <li><strong>总权重归一化：</strong>确保Σw_i(p) = 1，保持训练的一致性</li>
                    </ul>
                </li>
            </ol>

            <h3>3.8 理论引导的训练策略</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>域适应理论指导</h4>
                    <p><strong>H-divergence理论：</strong>基于Ben-David等人的域适应理论，通过最小化源域和目标域的H-divergence来提升泛化性能。</p>
                    <p><strong>实现方式：</strong>多判别器的渐进式训练策略确保了H-divergence的逐步减小，避免了训练过程中的震荡。</p>
                </div>
                <div class="feature-card">
                    <h4>课程学习策略</h4>
                    <p><strong>难度递增：</strong>从简单的二分类判别器开始，逐步增加网络复杂度和判别难度，符合课程学习的"由易到难"原则。</p>
                    <p><strong>知识迁移：</strong>前期判别器学到的域不变特征为后期复杂判别器提供良好的初始化，加速收敛。</p>
                </div>
            </div>
        </div>

        <h2>创新点四：对比分析与性能提升 <span class="innovation-badge">效果验证</span></h2>

        <div class="innovation-section">
            <h3>4.1 改进前后架构对比</h3>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>对比维度</th>
                        <th>原始ConvNeXt V2</th>
                        <th>改进ConvNeXtV2_atto</th>
                        <th>提升效果</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>卷积类型</td>
                        <td>标准深度卷积(7×7)</td>
                        <td>小波卷积(WTConv2d)</td>
                        <td>多尺度频域分析能力</td>
                    </tr>
                    <tr>
                        <td>注意力机制</td>
                        <td>仅GRN通道竞争</td>
                        <td>GRN + EMA多尺度注意力</td>
                        <td>空间-通道联合注意力</td>
                    </tr>
                    <tr>
                        <td>特征表达</td>
                        <td>单一空域特征</td>
                        <td>频域+空域融合特征</td>
                        <td>更丰富的特征表示</td>
                    </tr>
                    <tr>
                        <td>计算复杂度</td>
                        <td>基准</td>
                        <td>轻微增加(~15%)</td>
                        <td>可接受的计算开销</td>
                    </tr>
                </tbody>
            </table>

            <h3>4.2 TPTLN vs TPTLN-GLC++对比</h3>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>对比维度</th>
                        <th>原始TPTLN</th>
                        <th>TPTLN-GLC++</th>
                        <th>创新改进</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>伪标签生成</td>
                        <td>无专门机制</td>
                        <td>逐类二分类器</td>
                        <td>精确的伪标签生成</td>
                    </tr>
                    <tr>
                        <td>对比学习</td>
                        <td>无</td>
                        <td>GLC++全局局部对比</td>
                        <td>增强特征判别性</td>
                    </tr>
                    <tr>
                        <td>特征投影</td>
                        <td>无</td>
                        <td>专门投影层</td>
                        <td>对比学习特征优化</td>
                    </tr>
                    <tr>
                        <td>学习策略</td>
                        <td>单一域对抗</td>
                        <td>多任务联合优化</td>
                        <td>更稳定的训练过程</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <h2>创新点五：技术实现细节与关键代码 <span class="innovation-badge">实现细节</span></h2>

        <div class="innovation-section">
            <h3>5.1 小波卷积核心实现</h3>
            <div class="code-block">
def create_wavelet_filter(wavelet, in_channels, out_channels, dtype):
    """创建小波滤波器"""
    w = pywt.Wavelet(wavelet)
    dec_hi = torch.tensor(w.dec_hi[::-1], dtype=dtype)
    dec_lo = torch.tensor(w.dec_lo[::-1], dtype=dtype)

    # 构建小波变换滤波器
    filters = torch.stack([dec_lo.unsqueeze(0) * dec_lo.unsqueeze(1),
                          dec_lo.unsqueeze(0) * dec_hi.unsqueeze(1),
                          dec_hi.unsqueeze(0) * dec_lo.unsqueeze(1),
                          dec_hi.unsqueeze(0) * dec_hi.unsqueeze(1)], dim=0)

    return filters.unsqueeze(1).repeat(in_channels, 1, 1, 1)
            </div>

            <h3>5.2 EMA注意力机制实现</h3>
            <div class="code-block">
class EMA(nn.Module):
    def __init__(self, channels, factor=32):
        super(EMA, self).__init__()
        self.groups = factor
        self.softmax = nn.Softmax(-1)
        self.agp = nn.AdaptiveAvgPool2d((1, 1))
        self.pool_h = nn.AdaptiveAvgPool2d((None, 1))
        self.pool_w = nn.AdaptiveAvgPool2d((1, None))
        self.gn = nn.GroupNorm(channels // self.groups, channels // self.groups)
        self.conv1x1 = nn.Conv2d(channels // self.groups, channels // self.groups, 1)
        self.conv3x3 = nn.Conv2d(channels // self.groups, channels // self.groups, 3, padding=1)

    def forward(self, x):
        b, c, h, w = x.size()
        group_x = x.reshape(b * self.groups, -1, h, w)

        # 多尺度池化
        x_h = self.pool_h(group_x)
        x_w = self.pool_w(group_x).permute(0, 1, 3, 2)
        hw = self.conv1x1(torch.cat([x_h, x_w], dim=2))

        # 注意力权重计算
        x_h, x_w = torch.split(hw, [h, w], dim=2)
        x_w = x_w.permute(0, 1, 3, 2)

        # 特征增强
        a_h = self.softmax(x_h)
        a_w = self.softmax(x_w)
        out = group_x * a_h.expand_as(group_x) * a_w.expand_as(group_x)

        return out.reshape(b, c, h, w)
            </div>

            <h3>5.3 GLC++对比学习实现</h3>
            <div class="code-block">
def generate_pseudo_labels_glc_plus(self, features, predictions):
    """GLC++伪标签生成"""
    # 全局原型计算
    for cls_idx in range(self.num_classes):
        # 获取高置信度样本
        pos_indices = sorted_indices[:pos_topk_num, cls_idx]
        pos_features = all_features[pos_indices]

        # 计算类别原型
        class_prototype = pos_features.mean(dim=0, keepdim=True)
        class_prototype = F.normalize(class_prototype, dim=1)

        # 原型相似度计算
        similarity = torch.mm(all_features, class_prototype.t()).squeeze()
        pseudo_labels[:, cls_idx] = similarity * (1.0 - self.rho) + self.rho

    # 局部聚类细化
    for cls_idx in range(self.num_classes):
        high_conf_mask = (pseudo_labels[:, cls_idx] > 0.7)
        if high_conf_mask.sum() > 50:
            # Mini-batch KMeans聚类
            high_conf_features = all_features[high_conf_mask]
            kmeans = MiniBatchKMeans(n_clusters=self.local_k, random_state=42)
            cluster_labels = kmeans.fit_predict(high_conf_features.cpu().numpy())
            # 基于聚类结果细化伪标签...
            </div>
        </div>

        <h2>总结与展望</h2>

        <div class="innovation-section">
            <h3>主要创新贡献</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>1. 小波卷积增强</h4>
                    <p>首次将小波变换引入ConvNeXt V2架构，实现多尺度频域特征分析，显著提升网络对复杂信号的处理能力。</p>
                </div>
                <div class="feature-card">
                    <h4>2. EMA注意力集成</h4>
                    <p>创新性地在ConvNeXt V2中集成EMA多尺度注意力机制，实现空间-通道联合注意力建模。</p>
                </div>
                <div class="feature-card">
                    <h4>3. GLC++对比学习</h4>
                    <p>提出全局局部对比学习机制，结合原型学习和局部聚类，提高伪标签质量和特征判别性。</p>
                </div>
                <div class="feature-card">
                    <h4>4. 理论引导渐进式学习</h4>
                    <p>设计多判别器渐进式训练策略，基于域适应理论指导训练过程，提升跨域迁移效果。</p>
                </div>
            </div>

            <div class="highlight">
                <strong>技术创新意义：</strong>本研究提出的两个网络模型在保持计算效率的同时，显著提升了特征表达能力和跨域泛化性能。小波卷积和EMA注意力的结合为卷积神经网络的改进提供了新思路，而TPTLN-GLC++的多任务学习框架为域适应问题提供了更加完善的解决方案。这些创新为故障诊断、图像识别等应用领域的深度学习模型优化奠定了重要基础。
            </div>
        </div>
    </div>
</body>
</html>
