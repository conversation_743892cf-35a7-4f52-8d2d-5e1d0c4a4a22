import os
import json
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix
import argparse

def visualize_confusion_matrix(pred_file, save_dir='results/confmtx_img'):
    """
    将预测结果JSON文件转换为混淆矩阵图像

    参数:
        pred_file: 预测结果JSON文件路径
        save_dir: 保存图像的目录
    """
    # 创建保存目录
    os.makedirs(save_dir, exist_ok=True)

    # 读取JSON文件
    with open(pred_file, 'r') as f:
        data = json.load(f)

    # 获取预测和真实标签
    if isinstance(data, dict):
        if 'predictions' in data and 'labels' in data:
            y_pred = data['predictions']
            y_true = data['labels']
        elif 'pred' in data and 'true' in data:
            y_pred = data['pred']
            y_true = data['true']
        elif 'features' in data and 'label' in data:
            # 注意：这里'features'实际上是预测结果，'label'是真实标签
            y_pred = data['features']
            y_true = data['label']
        else:
            # 尝试自动检测字段
            keys = list(data.keys())
            print(f"文件 {pred_file} 包含的字段: {keys}")

            # 尝试其他可能的字段名组合
            pred_keys = ['predictions', 'pred', 'features', 'y_pred', 'predicted']
            true_keys = ['labels', 'true', 'label', 'y_true', 'ground_truth']

            pred_key = None
            true_key = None

            for key in pred_keys:
                if key in data:
                    pred_key = key
                    break

            for key in true_keys:
                if key in data:
                    true_key = key
                    break

            if pred_key and true_key:
                y_pred = data[pred_key]
                y_true = data[true_key]
                print(f"自动检测到字段: 预测={pred_key}, 真实={true_key}")
            else:
                print(f"文件格式不支持: {pred_file} - 找不到预测和标签字段")
                print(f"支持的预测字段: {pred_keys}")
                print(f"支持的标签字段: {true_keys}")
                return
    else:
        print(f"文件格式不支持: {pred_file} - 不是JSON对象")
        return

    # 转换为numpy数组并进行数据验证
    y_pred = np.array(y_pred)
    y_true = np.array(y_true)

    # 详细的数据验证和调试信息
    print(f"\n=== 数据统计信息 ===")
    print(f"文件: {os.path.basename(pred_file)}")
    print(f"预测数据长度: {len(y_pred)}")
    print(f"真实标签长度: {len(y_true)}")
    print(f"预测数据类型: {y_pred.dtype}")
    print(f"真实标签类型: {y_true.dtype}")
    print(f"预测数据范围: {y_pred.min()} - {y_pred.max()}")
    print(f"真实标签范围: {y_true.min()} - {y_true.max()}")
    print(f"预测数据唯一值: {sorted(np.unique(y_pred))}")
    print(f"真实标签唯一值: {sorted(np.unique(y_true))}")

    # 检查数据长度
    if len(y_pred) != len(y_true):
        print(f"错误: 预测和真实标签长度不匹配: {len(y_pred)} vs {len(y_true)}")
        return

    # 检查数据是否为空
    if len(y_pred) == 0:
        print(f"错误: 数据为空")
        return

    # 检查数据范围是否合理
    if y_pred.min() < 0 or y_true.min() < 0:
        print(f"警告: 发现负数标签值")

    # 显示前10个样本用于调试
    print(f"前10个样本:")
    for i in range(min(10, len(y_pred))):
        print(f"  样本{i}: 预测={y_pred[i]}, 真实={y_true[i]}")

    if len(y_pred) > 10:
        print(f"  ... (共{len(y_pred)}个样本)")

    # 检查预测和真实标签的分布
    pred_counts = np.bincount(y_pred)
    true_counts = np.bincount(y_true)
    print(f"预测标签分布: {dict(enumerate(pred_counts))}")
    print(f"真实标签分布: {dict(enumerate(true_counts))}")

    print(f"开始生成混淆矩阵...")

    # 计算混淆矩阵
    labels = sorted(list(set(y_true)))
    cm = confusion_matrix(y_true, y_pred, labels=labels)

    print(f"混淆矩阵形状: {cm.shape}")
    print(f"混淆矩阵:\n{cm}")

    # 设置图像大小
    plt.figure(figsize=(10, 8))

    # 检查是ABCD_SLT还是P1P2P3P4_SLT数据集
    filename = os.path.basename(pred_file).replace('.json', '')

    # 根据文件名或标签数量判断数据集类型
    is_abcd_dataset = any(domain in filename for domain in ['A_SLT', 'B_SLT', 'C_SLT', 'D_SLT']) or len(labels) == 10
    is_p_dataset = any(domain in filename for domain in ['P1_SLT', 'P2_SLT', 'P3_SLT', 'P4_SLT']) or len(labels) == 4

    if is_abcd_dataset:
        # ABCD_SLT数据集的标签
        custom_labels = ['Norm', 'IF-0.007', 'BF-0.007', 'OF-0.007',
                         'IF-0.014', 'BF-0.014', 'OF-0.014',
                         'IF-0.021', 'BF-0.021', 'OF-0.021']
        plt.figure(figsize=(14, 12))

        # 使用与截图一致的紫色调色板
        cmap = plt.cm.Purples
        # 设置更高的vmax以匹配截图中的颜色强度
        # 增大字体大小，annot_kws控制混淆矩阵中数字的大小
        heatmap = sns.heatmap(cm, annot=True, fmt='d', cmap=cmap, vmin=0, vmax=40,
                    xticklabels=custom_labels, yticklabels=custom_labels,
                    annot_kws={"size": 18}, cbar_kws={'label': '', 'ticks': [0, 5, 10, 15, 20, 25, 30, 35, 40]})

        # 设置颜色条刻度标签字体大小
        cbar = heatmap.collections[0].colorbar
        cbar.ax.tick_params(labelsize=18)

        # 完全去掉标签标题
        plt.xlabel('')
        plt.ylabel('')

        # 设置x轴标签逆时针旋转45度
        plt.xticks(rotation=45, fontsize=18)
        # 设置y轴标签水平显示
        plt.yticks(rotation=0, fontsize=18)

    elif is_p_dataset:
        # P1P2P3P4_SLT数据集的标签
        custom_labels = ['Norm', 'OF', 'IF', 'OF+IF']
        plt.figure(figsize=(10, 10))

        # 使用与截图一致的紫色调色板
        cmap = plt.cm.Purples
        # 设置更高的vmax以匹配截图中的颜色强度
        # 增大字体大小，annot_kws控制混淆矩阵中数字的大小
        heatmap = sns.heatmap(cm, annot=True, fmt='d', cmap=cmap, vmin=0, vmax=80,
                    xticklabels=custom_labels, yticklabels=custom_labels,
                    annot_kws={"size": 20}, cbar_kws={'label': '', 'ticks': [0, 10, 20, 30, 40, 50, 60, 70, 80]})

        # 设置颜色条刻度标签字体大小
        cbar = heatmap.collections[0].colorbar
        cbar.ax.tick_params(labelsize=20)

        # 完全去掉标签标题
        plt.xlabel('')
        plt.ylabel('')

        # 设置x轴标签逆时针旋转45度
        plt.xticks(rotation=45, fontsize=20)
        # 设置y轴标签水平显示
        plt.yticks(rotation=0, fontsize=20)

    else:
        # 默认情况
        plt.figure(figsize=(12, 10))
        heatmap = sns.heatmap(cm, annot=True, fmt='d', cmap='Purples',
                    xticklabels=labels, yticklabels=labels,
                    annot_kws={"size": 18}, cbar_kws={'label': ''})

        # 设置颜色条刻度标签字体大小
        cbar = heatmap.collections[0].colorbar
        cbar.ax.tick_params(labelsize=18)

        # 完全去掉标签标题
        plt.xlabel('')
        plt.ylabel('')

        # 设置x轴标签逆时针旋转45度
        plt.xticks(rotation=45, fontsize=18)
        # 设置y轴标签水平显示
        plt.yticks(rotation=0, fontsize=18)

    # 标签已在各个条件分支中设置，此处不需要重复设置

    # 提取文件名(不含路径和扩展名)
    filename = os.path.basename(pred_file).replace('.json', '')

    # 保存图像
    save_path = os.path.join(save_dir, f"{filename}.png")
    plt.tight_layout()
    plt.savefig(save_path, dpi=300)
    plt.close()

    print(f"混淆矩阵图像已保存至: {save_path}")

def process_all_files(pred_dir='results/confmtx_pred', save_dir='results/confmtx_img'):
    """处理预测目录中的所有JSON文件"""
    # 获取脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))

    # 构建绝对路径
    abs_pred_dir = os.path.join(script_dir, pred_dir)
    abs_save_dir = os.path.join(script_dir, save_dir)

    # 确保目录存在
    if not os.path.exists(abs_pred_dir):
        print(f"警告: 预测结果目录不存在: {abs_pred_dir}")
        print("尝试在当前工作目录下查找...")

        # 尝试在当前工作目录查找
        cwd_pred_dir = os.path.join(os.getcwd(), pred_dir)
        if os.path.exists(cwd_pred_dir):
            abs_pred_dir = cwd_pred_dir
            print(f"找到目录: {abs_pred_dir}")
        else:
            # 尝试查找其他可能的位置
            possible_pred_dir = os.path.join(os.getcwd(), "DeepLearn_Fault/DeepLearn_Fault", pred_dir)
            if os.path.exists(possible_pred_dir):
                abs_pred_dir = possible_pred_dir
                abs_save_dir = os.path.join(os.getcwd(), "DeepLearn_Fault/DeepLearn_Fault", save_dir)
                print(f"找到目录: {abs_pred_dir}")
            else:
                raise FileNotFoundError(f"无法找到预测结果目录: {pred_dir}")

    print(f"使用预测目录: {abs_pred_dir}")
    print(f"使用保存目录: {abs_save_dir}")

    # 创建保存目录
    os.makedirs(abs_save_dir, exist_ok=True)

    # 获取所有JSON文件
    json_files = [os.path.join(abs_pred_dir, f) for f in os.listdir(abs_pred_dir) if f.endswith('.json')]

    if not json_files:
        print(f"警告: 在目录 {abs_pred_dir} 中找不到任何JSON文件")
        return

    print(f"找到 {len(json_files)} 个JSON文件")

    # 处理每个文件
    for json_file in json_files:
        try:
            visualize_confusion_matrix(json_file, abs_save_dir)
        except Exception as e:
            print(f"处理文件 {json_file} 时出错: {e}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="将混淆矩阵JSON文件转换为图像")
    parser.add_argument('--pred_dir', type=str, default='results/confmtx_pred',
                        help='预测结果JSON文件目录')
    parser.add_argument('--save_dir', type=str, default='results/confmtx_img',
                        help='保存图像的目录')
    parser.add_argument('--single_file', type=str, default=None,
                        help='处理单个JSON文件(如果指定)')

    args = parser.parse_args()

    if args.single_file:
        visualize_confusion_matrix(args.single_file, args.save_dir)
    else:
        process_all_files(args.pred_dir, args.save_dir)